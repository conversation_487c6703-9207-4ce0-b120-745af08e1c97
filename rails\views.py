from django.shortcuts import render
from rest_framework import viewsets
from .models import RailServiceProvider, RailServiceProviderMasRailwayLine, RailwayEngineeringStandard, AgencySupportRailwayResearch, UnderConstruction
from mas.models import MasRailwayLine, MasTrainType
from .serializers import (
    RailServiceProviderSerializer,
    RailServiceProviderMasRailwayLineSerializer,
    RailwayEngineeringStandardSerializer,
    AgencySupportRailwayResearchSerializer,
    UnderConstructionSerializer,
    UpdateRailServiceProviderSerializer,
)
from mas.serializers import MasRailwayLineSerializer
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from utils.pagination import CustomPagination
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.response import Response
from utils.util import convert_str_to_bool
from rest_framework.decorators import action
from django.core.paginator import Paginator
from rest_framework.parsers import MultiPartPars<PERSON>, <PERSON><PERSON>ars<PERSON>, JSONParser
import io
import pandas as pd
from datetime import datetime
import urllib.parse
from django.http import HttpResponse
from rest_framework.permissions import AllowAny
from rest_framework import status
from users.models import User
from django.db import transaction

@extend_schema(
    tags=["Rail Service"]
)
class RailServiceProviderViewSet(viewsets.ModelViewSet):
    queryset = RailServiceProvider.objects.all()
    serializer_class = RailServiceProviderSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name', 'masTrainType__id', 'status']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Railway Service Provider data</h1>
        """
        name = request.query_params.get('name')
        masTrainTypeId = request.query_params.get('masTrainType__id')
        masRailwayLineId = request.query_params.get('masRailwayLine__id')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        
        # Apply status filter if provided
        if name:
            self.queryset = self.queryset.filter(name__icontains=name)
        if masTrainTypeId:
            self.queryset = self.queryset.filter(masTrainType__id=masTrainTypeId)
        if status:
            self.queryset = self.queryset.filter(status=convert_str_to_bool(status))
        if masRailwayLineId:
            railway_providers_with_line = RailServiceProviderMasRailwayLine.objects.filter(
                masRailwayLine__id=masRailwayLineId
            ).values_list('railServiceProvider_id', flat=True)
            print('railway_providers_with_line : ', railway_providers_with_line)
            self.queryset = self.queryset.filter(id__in=railway_providers_with_line)
        if ordering:
            self.queryset = self.queryset.order_by(ordering)
             
        # Apply pagination
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
        else:
            serializer = self.get_serializer(queryset, many=True)
            data = serializer.data

        # Add masRailwayLine data to each rail service provider
        for item in data:
            railway_service_provider_mas_railway_line = RailServiceProviderMasRailwayLine.objects.filter(
                railServiceProvider__id=item['id']
            )
            # Get the railway line IDs
            railway_line_ids = railway_service_provider_mas_railway_line.values_list(
                'masRailwayLine', flat=True)

            # Fetch the actual MasRailwayLine objects
            railway_lines = MasRailwayLine.objects.filter(
                id__in=railway_line_ids)

            # Serialize the MasRailwayLine objects
            railway_lines_serialized = MasRailwayLineSerializer(
                railway_lines, many=True).data

            # Add the serialized objects to the response
            item['masRailwayLines'] = railway_lines_serialized

        # Return paginated response if pagination was applied
        if page is not None:
            return self.get_paginated_response(data)

        return Response(data)

    def retrieve(self, request, *args, **kwargs):
        """
        <h1>Retrieve Railway Service Provider data</h1>
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data

        # Add masRailwayLine data to the rail service provider
        railway_service_provider_mas_railway_line = RailServiceProviderMasRailwayLine.objects.filter(
            railServiceProvider__id=data['id']
        )
        # Get the railway line IDs
        railway_line_ids = railway_service_provider_mas_railway_line.values_list(
            'masRailwayLine', flat=True)

        # Fetch the actual MasRailwayLine objects
        railway_lines = MasRailwayLine.objects.filter(id__in=railway_line_ids)

        # Serialize the MasRailwayLine objects
        railway_lines_serialized = MasRailwayLineSerializer(
            railway_lines, many=True).data

        # Add the serialized objects to the response
        data['masRailwayLines'] = railway_lines_serialized

        return Response(data)

    @action(detail=False, methods=['get'])
    def group(self, request, *args, **kwargs):
        """
        <h1>List Railway Service Provider By MasTrainType Group</h1>
        """
        status = request.query_params.get('status')
        if status:
            status = convert_str_to_bool(status)
            self.queryset = self.queryset.filter(status=status)
        # Apply pagination
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
        else:
            serializer = self.get_serializer(queryset, many=True)
            data = serializer.data

        # Add masRailwayLine data to each rail service provider
        for item in data:
            railway_service_provider_mas_railway_line = RailServiceProviderMasRailwayLine.objects.filter(
                railServiceProvider__id=item['id']
            )
            # Get the railway line IDs
            railway_line_ids = railway_service_provider_mas_railway_line.values_list(
                'masRailwayLine', flat=True)

            # Fetch the actual MasRailwayLine objects
            railway_lines = MasRailwayLine.objects.filter(
                id__in=railway_line_ids)

            # Serialize the MasRailwayLine objects
            railway_lines_serialized = MasRailwayLineSerializer(
                railway_lines, many=True).data

            # Add the serialized objects to the response
            item['masRailwayLines'] = railway_lines_serialized

        # Group data by MasTrainType
        grouped_data = {}
        for item in data:
            train_type_id = item['masTrainType']['id']
            train_type_name = item['masTrainType']['name']

            if train_type_id not in grouped_data:
                grouped_data[train_type_id] = {
                    'id': train_type_id,
                    'name': train_type_name,
                    'railServiceProviders': []
                }

            grouped_data[train_type_id]['railServiceProviders'].append(item)

        # Convert dictionary to list for response
        result = list(grouped_data.values())

        # Apply pagination to the grouped result
        page_size = self.paginator.get_page_size(request)
        if page_size is not None:
            paginator = Paginator(result, page_size)
            page_number = request.query_params.get(
                self.paginator.page_query_param, 1)
            page_obj = paginator.get_page(page_number)

            return Response({
                'count': paginator.count,
                'next': self.paginator.get_next_link() if page_obj.has_next() else None,
                'previous': self.paginator.get_previous_link() if page_obj.has_previous() else None,
                'results': page_obj.object_list
            })

        return Response(result)
    
    def update(self, request, *args, **kwargs):
        """
        <h1>Update Railway Service Provider data</h1>
        """
        id = self.kwargs.get('pk')
        name = request.data.get('name')
        status = request.data.get('status')
        masTrainTypeId = request.data.get('masTrainTypeId')
        link = request.data.get('link')
        logo = request.data.get('logo')
        obj = RailServiceProvider.objects.get(id=id)
        if name:
            obj.name = name
        if status:
            obj.status = convert_str_to_bool(status)
        if masTrainTypeId:
            obj.masTrainType_id = masTrainTypeId
        if link:
            obj.link = link
        if logo and not str(logo).__contains__(str(obj.logo)):
            obj.logo = logo
        obj.save()
        serializer = self.get_serializer(obj)
        return Response(serializer.data)

    @action(detail=False, methods=['put'], url_path='update-mas-railway-line/(?P<pk>[^/.]+)', serializer_class=UpdateRailServiceProviderSerializer, parser_classes=[JSONParser])
    def update_mas_railway_line(self, request, *args, **kwargs):
        railServiceProviderId = self.kwargs.get('pk')
        masRailwayLineIds = request.data.get('masRailwayLines')
        if masRailwayLineIds:
            try:
                # Get the RailServiceProvider object
                railServiceProvider = RailServiceProvider.objects.get(id=railServiceProviderId)
                
                # Get existing relationships
                existing_relationships = RailServiceProviderMasRailwayLine.objects.filter(
                    railServiceProvider__id=railServiceProviderId)
                
                # Get existing masRailwayLine IDs
                existing_ids = [rel.masRailwayLine_id for rel in existing_relationships]
                
                # Convert new IDs to integers to ensure proper comparison
                new_ids = [int(id) for id in masRailwayLineIds]
                
                # Check if the sets of IDs are different
                if set(existing_ids) != set(new_ids):
                    # Delete existing relationships only if they're different
                    existing_relationships.delete()
                    
                    # Create new relationships
                    for masRailwayLineId in new_ids:
                        # Create the relationship with proper foreign keys
                        RailServiceProviderMasRailwayLine.objects.create(
                            railServiceProvider_id=railServiceProviderId,
                            masRailwayLine_id=masRailwayLineId
                        )
                    
                    message = "Railway lines updated successfully"
                else:
                    message = "No changes needed - railway lines already up to date"
                
                # Return a success response
                return Response({
                    "success": True,
                    "message": message,
                    "railServiceProviderId": railServiceProviderId,
                    "masRailwayLines": new_ids
                })
            except Exception as e:
                return Response({"error": str(e)}, status=400)
        else:
            return Response({"message": "No masRailwayLineIds provided"})

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลผู้ให้บริการระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of railway service provider IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        masTrainTypeId = request.data.get('masTrainType__id')
        masRailwayLineId = request.data.get('masRailwayLine__id')
        status = request.data.get('status')    
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if masTrainTypeId:
            queryset = queryset.filter(masTrainType__id=masTrainTypeId)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if masRailwayLineId:
            railway_providers_with_line = RailServiceProviderMasRailwayLine.objects.filter(
                masRailwayLine__id=masRailwayLineId
            ).values_list('railServiceProvider_id', flat=True)
            print('railway_providers_with_line : ', railway_providers_with_line)
            queryset = queryset.filter(id__in=railway_providers_with_line)
            
        for item in queryset:
            try:
                masTrainType = MasTrainType.objects.get(id=item.masTrainType.id)
                item.masTrainTypeName = masTrainType.name
                railwayServiceProviderMasRailwayLine = RailServiceProviderMasRailwayLine.objects.filter(railServiceProvider_id=item.id)
                railwayLineIds = railwayServiceProviderMasRailwayLine.values_list('masRailwayLine_id', flat=True)
                railwayLines = MasRailwayLine.objects.filter(id__in=railwayLineIds)
                railwayLineNames = railwayLines.values_list('name', flat=True)
                item.masRailwayLineName = ", ".join(railwayLineNames)
            except MasTrainType.DoesNotExist:
                item.masTrainTypeName = None
            except RailServiceProviderMasRailwayLine.DoesNotExist:
                item.masRailwayLineName = None
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อผู้ให้บริการ': item.name,
                'ประเภท': item.masTrainTypeName,
                'สายรถไฟ': item.masRailwayLineName,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
            
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลผู้ให้บริการระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:F1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:F2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Rail Service"]
)
class RailServiceProviderMasRailwayLineViewSet(viewsets.ModelViewSet):
    queryset = RailServiceProviderMasRailwayLine.objects.all()
    serializer_class = RailServiceProviderMasRailwayLineSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]


@extend_schema(
    tags=["Rail Service"]
)
class RailwayEngineeringStandardViewSet(viewsets.ModelViewSet):
    queryset = RailwayEngineeringStandard.objects.all()
    serializer_class = RailwayEngineeringStandardSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    search_fields = ['name']
    filterset_fields = ['name', 'status']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Railway Engineering Standard data</h1>
        <h2>Parameters:(Querystring)</h2>
        <ul>
            <li>name: str</li>
            <li>status: bool</li>
        </ul>
        """
        queryset = self.get_queryset()
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """
        <h1>Update Railway Engineering Standard data</h1>
        """
        id = self.kwargs.get('pk')
        name = request.data.get('name')
        status = request.data.get('status')
        file = request.data.get('file')
        cover = request.data.get('cover')
        obj = RailwayEngineeringStandard.objects.get(id=id)
        if name:
            obj.name = name
        if status:
            obj.status = convert_str_to_bool(status)
        if file:
            obj.file = file
        if cover:
            obj.cover = cover
        obj.save()
        serializer = self.get_serializer(obj)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลประมวล มาตรฐานวิศวกรรม ระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of railway engineering standard IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        status = request.data.get('status')     
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
            
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อประมวลมาตรฐาน': item.name,
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลประมวล มาตรฐานวิศวกรรม ระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:E1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:E2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
    

@extend_schema(
    tags=["Rail Service"]
)
class AgencySupportRailwayResearchViewSet(viewsets.ModelViewSet):
    queryset = AgencySupportRailwayResearch.objects.all()
    serializer_class = AgencySupportRailwayResearchSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name', 'status']
    search_fields = ['name', 'status']
    
    def list(self, request, *args, **kwargs):
        """
        <h1>List Agency Support Railway Research data</h1>
        """
        queryset = self.get_queryset()
        name = request.query_params.get('name')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
            return self.get_paginated_response(data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """
        <h1>Update Agency Support Railway Research data</h1>
        """
        id = self.kwargs.get('pk')
        name = request.data.get('name')
        status = request.data.get('status')
        link = request.data.get('link')
        logo = request.data.get('logo')
        obj = AgencySupportRailwayResearch.objects.get(id=id)
        if name:
            obj.name = name
        if status:
            obj.status = convert_str_to_bool(status)
        if link:
            obj.link = link
        if logo and not str(logo).__contains__(str(obj.logo)):
            obj.logo = logo
        obj.save()
        serializer = self.get_serializer(obj)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลหน่วยงานที่สนับสนุนงานวิจัยระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Agency Support Railway Research IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        status = request.data.get('status')    
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
            
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)
        
        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อหน่วยงาน': item.name,
                'ลิงก์เว็บไซต์': item.link,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลหน่วยงานที่สนับสนุนงานวิจัยระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:E1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:E2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Rail Service"]
)
class UnderConstructionViewSet(viewsets.ModelViewSet):
    queryset = UnderConstruction.objects.all()
    serializer_class = UnderConstructionSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name','code','detail','masRailwayLine__id','latitude','longitude','status']

    def list(self, request, *args, **kwargs):
        """
        <h1>List Under Construction data</h1>
        """
        queryset = self.get_queryset()
        name = request.query_params.get('name')
        code = request.query_params.get('code')
        detail = request.query_params.get('detail')
        masRailwayLineId = request.query_params.get('masRailwayLine__id')
        latitude = request.query_params.get('latitude')
        longitude = request.query_params.get('longitude')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering')
        if name:
            queryset = queryset.filter(name__icontains=name)
        if code:
            queryset = queryset.filter(code__icontains=code)
        if detail:
            queryset = queryset.filter(detail__icontains=detail)
        if masRailwayLineId:
            queryset = queryset.filter(masRailwayLine__id=masRailwayLineId)
        if latitude:
            queryset = queryset.filter(latitude=latitude)
        if longitude:
            queryset = queryset.filter(longitude=longitude)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
            return self.get_paginated_response(data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_under_construction(self, request, *args, **kwargs):
        """
        <h1>Import Under Construction data from list</h1>
        <p>Request Body:</p>
        <pre>
        [
            {
                "code": "UC001",
                "name": "Station Name",
                "detail": "Station Description",
                "masRailwayLine": "Railway Line Name",
                "latitude": 13.7563,
                "longitude": 100.5018,
                "status": true
            }
        ]
        </pre>
        <p><strong>Notes:</strong></p>
        <ul>
            <li>latitude and longitude must be valid numeric values (not placeholders like 'xxx.xxxx')</li>
            <li>latitude must be between -90 and 90</li>
            <li>longitude must be between -180 and 180</li>
            <li>masRailwayLine will be created if it doesn't exist</li>
            <li>All fields except status are required</li>
        </ul>
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of under construction objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_items = []
        errors = []

        try:
            with transaction.atomic():
                for index, item_data in enumerate(request.data):
                    try:
                        # Validate required fields
                        required_fields = ['code', 'name', 'detail', 'masRailwayLine', 'latitude', 'longitude']
                        missing_fields = [field for field in required_fields if field not in item_data or item_data[field] is None or str(item_data[field]).strip() == '']

                        if missing_fields:
                            errors.append({
                                "index": index,
                                "data": item_data,
                                "error": f"Missing required fields: {', '.join(missing_fields)}"
                            })
                            continue

                        # Validate and convert latitude
                        try:
                            latitude_str = str(item_data['latitude']).strip()
                            if latitude_str.lower() in ['xxx.xxxx', 'x.xxxx', 'xx.xxxx', 'xxxx.xxxx', '', 'null', 'none']:
                                raise ValueError("Invalid latitude placeholder")
                            latitude = float(latitude_str)
                            if not (-90 <= latitude <= 90):
                                raise ValueError("Latitude must be between -90 and 90")
                        except (ValueError, TypeError) as e:
                            latitude = 0.0

                        # Validate and convert longitude
                        try:
                            longitude_str = str(item_data['longitude']).strip()
                            if longitude_str.lower() in ['xxx.xxxx', 'x.xxxx', 'xx.xxxx', 'xxxx.xxxx', '', 'null', 'none']:
                                raise ValueError("Invalid longitude placeholder")
                            longitude = float(longitude_str)
                            if not (-180 <= longitude <= 180):
                                raise ValueError("Longitude must be between -180 and 180")
                        except (ValueError, TypeError) as e:
                            longitude = 0.0

                        # Get or create masRailwayLine
                        try:
                            railway_line_name = str(item_data['masRailwayLine']).strip()
                            if not railway_line_name:
                                raise ValueError("Railway line name cannot be empty")
                            mas_railway_line, _ = MasRailwayLine.objects.get_or_create(
                                name=railway_line_name,
                                defaults={'codeColor': '#000000'}  # Default color if creating new
                            )
                        except Exception as e:
                            errors.append({
                                "index": index,
                                "data": item_data,
                                "error": f"Error processing railway line '{item_data['masRailwayLine']}': {str(e)}"
                            })
                            continue

                        # Prepare data for serializer
                        data = {
                            'code': str(item_data['code']).strip(),
                            'name': str(item_data['name']).strip(),
                            'detail': str(item_data['detail']).strip(),
                            'masRailwayLineId': mas_railway_line.id,
                            'latitude': latitude,
                            'longitude': longitude,
                            'status': item_data.get('status', True)
                        }

                        # Create the under construction item
                        serializer = self.get_serializer(data=data)
                        if serializer.is_valid():
                            serializer.save()
                            created_items.append(serializer.data)
                        else:
                            errors.append({
                                "index": index,
                                "data": item_data,
                                "errors": serializer.errors
                            })

                    except ValueError as e:
                        errors.append({
                            "index": index,
                            "data": item_data,
                            "error": f"Invalid data type: {str(e)}"
                        })
                    except Exception as e:
                        errors.append({
                            "index": index,
                            "data": item_data,
                            "error": str(e)
                        })

                # If there are any errors, rollback the transaction
                if errors:
                    transaction.set_rollback(True)
                    return Response(
                        {
                            "error": "Import failed due to validation errors",
                            "errors": errors,
                            "created_count": 0,
                            "total_count": len(request.data)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            return Response(
                {
                    "message": f"Import completed successfully. Created {len(created_items)} under construction records.",
                    "created_items": created_items,
                    "created_count": len(created_items),
                    "total_count": len(request.data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "message": str(e),
                    "created_count": len(created_items)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลโครงการกำลังก่อสร้าง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of under construction IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        code = request.data.get('code')
        detail = request.data.get('detail')
        masRailwayLineId = request.data.get('masRailwayLine__id')
        latitude = request.data.get('latitude')
        longitude = request.data.get('longitude')
        status = request.data.get('status')   
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if code:
            queryset = queryset.filter(code__icontains=code)
        if detail:
            queryset = queryset.filter(detail__icontains=detail)
        if masRailwayLineId:
            queryset = queryset.filter(masRailwayLine__id=masRailwayLineId)
        if latitude:
            queryset = queryset.filter(latitude=latitude)
        if longitude:
            queryset = queryset.filter(longitude=longitude)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
            
        for item in queryset:
            try:
                masRailwayLine = MasRailwayLine.objects.get(id=item.masRailwayLine.id)
                item.masRailwayLineName = masRailwayLine.name
            except MasRailwayLine.DoesNotExist:
                item.masRailwayLine = None
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'รหัสสถานี': item.code,
                'ชื่อสถานีรถไฟ': item.name,
                'ที่ตั้งสถานี': item.detail,
                'ชื่อโครงการ': item.masRailwayLineName,
                'ละติจูด': item.latitude,
                'ลองจิจูด': item.longitude,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลโครงการกำลังก่อสร้าง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:I1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:I2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
