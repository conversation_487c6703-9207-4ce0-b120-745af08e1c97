# ServiceFares Import API Documentation

## Overview

The ServiceFares import API allows you to bulk import service fare data into the system. This endpoint accepts a list of fare records and creates them in the database with proper validation.

## Endpoint

```
POST /service-fares/import/?routeId={route_id}
```

## Authentication

This endpoint requires authentication. Include your authentication token in the request headers:

```
Authorization: Bearer <your_token>
```

## Request Format

### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| routeId   | int  | Yes      | ID of the route to associate with the fares |

### Request Body

The request body should be a JSON array of fare objects:

```json
[
  {
    "originStation": "สถานีต้นทาง",
    "destinationStation": "สถานีปลายทาง",
    "fareType": "N",
    "fareAmount": 150.0,
    "serviceType": "N",
    "frequency": "ความถี่ของบริการ",
    "operationStart": "06:00",
    "operationEnd": "22:00"
  }
]
```

### Field Descriptions

| Field | Type | Required | Description | Valid Values |
|-------|------|----------|-------------|--------------|
| originStation | string | Yes | Origin station name | Max 500 characters |
| destinationStation | string | Yes | Destination station name | Max 500 characters |
| fareType | string | Yes | Type of fare | N=ปกติ, C=เด็ก, O=ผู้สูงอายุ |
| fareAmount | float | Yes | Fare amount | Must be positive number |
| serviceType | string | Yes | Type of service | N=รถปกติ, E=รถด่วน |
| frequency | string | No | Service frequency | Max 500 characters |
| operationStart | string | No | Operation start time | Format: HH:MM |
| operationEnd | string | No | Operation end time | Format: HH:MM |

## Response Format

### Success Response (201 Created)

```json
{
  "message": "Import completed successfully. Created 3 servicefares records.",
  "created_servicefares": [
    {
      "id": 1,
      "routeId": 1,
      "originStation": "Bangkok Central",
      "destinationStation": "Chiang Mai Central",
      "fareType": "N",
      "fareAmount": 150.0,
      "serviceType": "N",
      "frequency": "Daily",
      "operationStart": "06:00",
      "operationEnd": "22:00"
    }
  ],
  "created_count": 3,
  "total_count": 3
}
```

### Error Response (400 Bad Request)

```json
{
  "error": "Import failed due to validation errors",
  "errors": [
    {
      "index": 0,
      "error": "Missing required fields: fareAmount",
      "data": { ... }
    }
  ],
  "created_count": 0,
  "total_count": 1
}
```

## Example Usage

### cURL Example

```bash
curl -X POST "http://localhost:8000/service-fares/import/?routeId=1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '[
    {
      "originStation": "Bangkok Central",
      "destinationStation": "Chiang Mai Central",
      "fareType": "N",
      "fareAmount": 150.0,
      "serviceType": "N",
      "frequency": "Daily",
      "operationStart": "06:00",
      "operationEnd": "22:00"
    }
  ]'
```

### Python Example

```python
import requests

url = "http://localhost:8000/service-fares/import/"
params = {"routeId": 1}
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer your_token_here"
}

data = [
    {
        "originStation": "Bangkok Central",
        "destinationStation": "Chiang Mai Central",
        "fareType": "N",
        "fareAmount": 150.0,
        "serviceType": "N",
        "frequency": "Daily",
        "operationStart": "06:00",
        "operationEnd": "22:00"
    }
]

response = requests.post(url, params=params, json=data, headers=headers)
print(response.json())
```

### JavaScript Example

```javascript
const url = 'http://localhost:8000/service-fares/import/?routeId=1';
const data = [
  {
    originStation: 'Bangkok Central',
    destinationStation: 'Chiang Mai Central',
    fareType: 'N',
    fareAmount: 150.0,
    serviceType: 'N',
    frequency: 'Daily',
    operationStart: '06:00',
    operationEnd: '22:00'
  }
];

fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_token_here'
  },
  body: JSON.stringify(data)
})
.then(response => response.json())
.then(result => console.log(result));
```

## Error Handling

The API performs comprehensive validation and will return detailed error messages for:

- Missing required fields
- Invalid fare types (must be N, C, or O)
- Invalid service types (must be N or E)
- Invalid fare amounts (must be positive numbers)
- Non-existent route IDs
- Malformed request data

All operations are performed within a database transaction, so if any record fails validation, the entire import is rolled back.

## Data Dictionary Reference

Based on the ServiceFares table structure:

- **FareType**: N=ปกติ (Normal), C=เด็ก (Child), O=ผู้สูงอายุ (Senior)
- **ServiceType**: N=รถปกติ (Regular train), E=รถด่วน (Express train)
- **Status**: Automatically set to 1 (Active) for all imported records
- **CreateUserId/CreateDate**: Automatically set based on authenticated user
- **UpdateUserId/UpdateDate**: Set when records are modified

## Notes

1. The `routeId` must exist in the Route table before importing fares
2. All imported records will have `status=True` by default
3. Optional fields (frequency, operationStart, operationEnd) can be omitted or set to empty strings
4. The API supports importing multiple records in a single request for efficiency
5. Use the provided example script (`import_example.py`) to test the functionality
