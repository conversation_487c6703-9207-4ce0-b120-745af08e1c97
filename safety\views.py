from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import transaction
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time, convert_str_to_date
from django.db.models import Q
from datetime import datetime

from .models import Safety
from .serializers import SafetySerializer


class CustomPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


@extend_schema(
    tags=["Safety"]
)
class SafetyViewSet(viewsets.ModelViewSet):
    queryset = Safety.objects.all()
    serializer_class = SafetySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['systemName', 'safetyFeature', 'safetyStandard','riskLevel','complianceStatus']
    search_fields = ['systemName', 'safetyFeature', 'safetyStandard','riskLevel','complianceStatus']

    def list(self, request, *args, **kwargs):
        systemName = request.query_params.get('systemName')
        safetyFeature = request.query_params.get('safetyFeature')
        safetyStandard = request.query_params.get('safetyStandard')
        riskLevel = request.query_params.get('riskLevel')
        complianceStatus = request.query_params.get('complianceStatus')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')

        if systemName:
            self.queryset = self.queryset.filter(systemName__icontains=systemName)
        if safetyFeature:
            self.queryset = self.queryset.filter(safetyFeature__icontains=safetyFeature)
        if safetyStandard:
            self.queryset = self.queryset.filter(safetyStandard__icontains=safetyStandard)
        if riskLevel:
            self.queryset = self.queryset.filter(riskLevel=riskLevel)
        if complianceStatus:
            self.queryset = self.queryset.filter(complianceStatus__icontains=complianceStatus)
        if startDate:
            self.queryset = self.queryset.filter(lastAuditDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            self.queryset = self.queryset.filter(lastAuditDate__lte=convert_str_to_date_max_time(endDate))
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            self.queryset = self.queryset.order_by(ordering)

        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_safety(self, request, *args, **kwargs):
        """
        Import safety data from list data
        Request Body:
        [
            {
                "systemName": "ตัวอย่างชื่อระบบราง",
                "safetyFeature": "ตัวรายการด้านความปลอดภัย",
                "safetyStandard": "ตัวอย่างมาตรฐานที่อ้างอิง",
                "riskLevel": "ตัวอย่างระดับความเสี่ยง",
                "lastAuditDate": "ตัวอย่างวันที่ตรวจสอบล่าสุด",
                "complianceStatus": "ตัวอย่างสถานะการปฏิบัติตามมาตรฐาน",
                "remark": "ตัวอย่างหมายเหตุ"
            }
        ]
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of safety objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_safety_records = []
        errors = []

        try:
            with transaction.atomic():
                for index, item in enumerate(request.data):
                    try:
                        # Validate required fields
                        required_fields = ['systemName', 'safetyFeature', 'safetyStandard', 'riskLevel']
                        missing_fields = []

                        for field in required_fields:
                            if not item.get(field):
                                missing_fields.append(field)

                        if missing_fields:
                            errors.append({
                                'index': index,
                                'error': f'Missing required fields: {", ".join(missing_fields)}'
                            })
                            continue
                        
                        if not item.get('riskLevel') or item['riskLevel'].upper() not in ['L', 'M', 'H', 'LOW', 'MEDIUM', 'HIGH']:
                            item['riskLevel'] = 'L'

                        # Create Safety record
                        safety_record = Safety.objects.create(
                            systemName=item['systemName'],
                            safetyFeature=item['safetyFeature'],
                            safetyStandard=item['safetyStandard'],
                            riskLevel=item['riskLevel'],
                            lastAuditDate=convert_str_to_date(item['lastAuditDate']),
                            complianceStatus=item.get('complianceStatus', ''),
                            remark=item.get('remark', '')
                        )

                        created_safety_records.append(safety_record)

                    except Exception as e:
                        errors.append({
                            'index': index,
                            'error': str(e)
                        })

                if errors:
                    # If there are errors, rollback the transaction
                    raise Exception("Import failed due to errors")

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "details": errors if errors else [str(e)]
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the created records for response
        serializer = SafetySerializer(created_safety_records, many=True)

        return Response(
            {
                "success": True,
                "message": f"Successfully imported {len(created_safety_records)} safety records",
                "created_records": serializer.data
            },
            status=status.HTTP_201_CREATED
        )

