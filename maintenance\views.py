from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOr<PERSON>eadOnly
from rest_framework_simplejwt.authentication import JWTAuthentication
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework.decorators import action
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import MaintenanceWork
from .serializers import MaintenanceWorkSerializer
from rest_framework.response import Response
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time
from django.db.models import Q
from django.db import transaction
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@extend_schema(
    tags=["Maintenance"]
)
class MaintenanceWorkViewSet(viewsets.ModelViewSet):
    """
    ViewSet for MaintenanceWork model providing CRUD operations.
    """
    queryset = MaintenanceWork.objects.all()
    serializer_class = MaintenanceWorkSerializer
    pagination_class = CustomPagination
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['code','systemComponent','maintenanceType','scheduleDate','actualDate','technician','status','costEstimate']
    search_fields = ['code','systemComponent','maintenanceType','scheduleDate','actualDate','technician','status','costEstimate']
    
    def list(self, request, *args, **kwargs):
        code = request.query_params.get('code')
        systemComponent = request.query_params.get('systemComponent')
        maintenanceType = request.query_params.get('maintenanceType')
        scheduleDate = request.query_params.get('scheduleDate')
        actualDate = request.query_params.get('actualDate')
        technician = request.query_params.get('technician')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        if code:
            self.queryset = self.queryset.filter(code__icontains=code)
        if systemComponent:
            self.queryset = self.queryset.filter(systemComponent__icontains=systemComponent)
        if maintenanceType:
            self.queryset = self.queryset.filter(maintenanceType=maintenanceType)
        if scheduleDate:
            self.queryset = self.queryset.filter(scheduleDate__gte=convert_str_to_date_min_time(scheduleDate))
        if actualDate:
            self.queryset = self.queryset.filter(actualDate__lte=convert_str_to_date_max_time(actualDate))
        if technician:
            self.queryset = self.queryset.filter(technician__icontains=technician)
        if status:
            self.queryset = self.queryset.filter(status=status)
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            self.queryset = self.queryset.order_by(ordering)
        
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='import')
    def import_maintenance_work(self, request):
        """
        Bulk import MaintenanceWork records from a list of data.

        Expected request body format:
        [
            {
                "code": "รหัสงานซ่อมบำรุง",
                "systemComponent": "ชิ้นส่วน/ระบบที่เกี่ยวข้อง",
                "maintenanceType": "ประเภทการซ่อมบำรุง",
                "scheduleDate": "วันที่นัดหมายซ่อมบำรุง (YYYY-MM-DD หรือ YYYY-MM-DDTHH:MM:SS)",
                "actualDate": "วันที่ดำเนินการจริง (YYYY-MM-DD หรือ YYYY-MM-DDTHH:MM:SS, optional)",
                "technician": "ชื่อผู้ดำเนินการ (optional)",
                "costEstimate": "ประมาณค่าใช่จ่าย (number)",
                "status": "สถานะ (boolean, optional, default: false)",
                "remark": "หมายเหตุ (optional)"
            },
            ...
        ]

        Returns:
        {
            "success": true,
            "message": "Successfully imported X records",
            "created_count": X,
            "errors": []  // List of validation errors if any
        }
        """
        try:
            # Validate request data
            if not isinstance(request.data, list):
                return Response(
                    {
                        'success': False,
                        'error': 'Expected a list of maintenance work records',
                        'message': 'Request body must be an array of objects'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not request.data:
                return Response(
                    {
                        'success': False,
                        'error': 'Empty data provided',
                        'message': 'Please provide at least one maintenance work record'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process and validate data
            errors = []
            created_count = 0

            # Create records in a transaction
            with transaction.atomic():
                for index, record_data in enumerate(request.data):
                    try:
                        processed_data = record_data.copy()

                        processed_data['scheduleDate'] = convert_str_to_date_min_time(processed_data['scheduleDate'])
                        if not processed_data['scheduleDate']:
                            processed_data['scheduleDate'] = datetime.now()

                        # Handle actualDate - it might not be present in the data
                        if 'actualDate' in processed_data and processed_data['actualDate']:
                            processed_data['actualDate'] = convert_str_to_date_max_time(processed_data['actualDate'])
                        else:
                            processed_data['actualDate'] = None

                        # Handle costEstimate validation
                        if 'costEstimate' in processed_data:
                            if processed_data['costEstimate'] is None:
                                processed_data['costEstimate'] = 0.0
                            elif isinstance(processed_data['costEstimate'], str):
                                # Try to convert string to float, handle Thai or non-numeric strings
                                cost_str = processed_data['costEstimate'].replace(',', '').strip()
                                try:
                                    # Check if string contains only digits, decimal point, and common separators
                                    if cost_str and any(c.isdigit() or c in '.,- ' for c in cost_str):
                                        # Remove any non-numeric characters except decimal point
                                        import re
                                        numeric_str = re.sub(r'[^\d.]', '', cost_str)
                                        if numeric_str:
                                            processed_data['costEstimate'] = float(numeric_str)
                                        else:
                                            processed_data['costEstimate'] = 0.0
                                    else:
                                        processed_data['costEstimate'] = 0.0
                                except (ValueError, TypeError):
                                    processed_data['costEstimate'] = 0.0
                            elif not isinstance(processed_data['costEstimate'], (int, float)):
                                processed_data['costEstimate'] = 0.0
                        else:
                            processed_data['costEstimate'] = 0.0

                        # Set default values
                        if 'status' not in processed_data:
                            processed_data['status'] = False
                        
                        if processed_data['maintenanceType'] not in ['P', 'C']:
                            processed_data['maintenanceType'] = 'P'

                        # Validate using existing MaintenanceWorkSerializer
                        serializer = MaintenanceWorkSerializer(data=processed_data)
                        if serializer.is_valid():
                            # Create the record directly
                            maintenance_work = MaintenanceWork(**serializer.validated_data)

                            # Try to get current user ID, if not available use system default
                            from RTRDA.middleware import get_current_user_id
                            user_id = get_current_user_id()
                            if user_id:
                                maintenance_work.createUserId = user_id
                            else:
                                # For bulk import without authentication, use system user ID 1
                                maintenance_work.createUserId = 1  # Assuming system user exists with ID 1

                            maintenance_work.save()
                            created_count += 1
                        else:
                            error_messages = []
                            for field, field_errors in serializer.errors.items():
                                error_messages.append(f"{field}: {', '.join(field_errors)}")
                            errors.append(f"Record {index + 1}: {'; '.join(error_messages)}")

                    except Exception as e:
                        errors.append(f"Record {index + 1}: Unexpected error - {str(e)}")

                # If there are validation errors, rollback transaction and return them
                if errors:
                    transaction.set_rollback(True)
                    return Response(
                        {
                            'success': False,
                            'message': f'Validation failed for {len(errors)} records',
                            'errors': errors,
                            'created_count': 0,
                            'total_records': len(request.data)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            logger.info(f"Successfully bulk imported {created_count} MaintenanceWork records")

            return Response(
                {
                    'success': True,
                    'message': f'Successfully imported {created_count} maintenance work records',
                    'created_count': created_count,
                    'errors': [],
                    'version': 'v.1.3.0'
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            logger.error(f"Error during bulk import of MaintenanceWork records: {str(e)}")
            return Response(
                {
                    'success': False,
                    'error': 'Internal server error during bulk import',
                    'message': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
