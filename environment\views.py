from rest_framework import viewsets, filters, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import Environment
from .serializers import EnvironmentSerializer
from rest_framework.response import Response
from django.db.models import Q
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time



@extend_schema(
    tags=["Environment"]
)
class EnvironmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Environment model providing CRUD operations.
    """
    queryset = Environment.objects.all()
    serializer_class = EnvironmentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['route__id','masProvince__id','masDistrict__id','parameter','value','unit','complianceStandard']
    search_fields = ['route__id','masProvince__id','masDistrict__id','parameter','value','unit','complianceStandard']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        route__id = request.query_params.get('route__id')
        masProvince__id = request.query_params.get('masProvince__id')
        masDistrict__id = request.query_params.get('masDistrict__id')
        parameter = request.query_params.get('parameter')
        value = request.query_params.get('value')
        unit = request.query_params.get('unit')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        complianceStandard = request.query_params.get('complianceStandard')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        if route__id:
            queryset = queryset.filter(route__id=route__id)
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        if parameter:
            queryset = queryset.filter(parameter__icontains=parameter)
        if value:
            queryset = queryset.filter(value=value)
        if unit:
            queryset = queryset.filter(unit__icontains=unit)
        if startDate:
            queryset = queryset.filter(measurementDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(measurementDate__lte=convert_str_to_date_max_time(endDate))
        if complianceStandard:
            queryset = queryset.filter(complianceStandard__icontains=complianceStandard)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_environment(self, request):
        """
        Import Environment data from list data
        Request Body:
        [
            {
                "parameter": "ตัวอย่างพารามิเตอร์สิ่งแวดล้อม",
                "value": "ตัวอย่างค่าที่วัดได้",
                "unit": "ตัวอย่างหน่วยวัด",
                "measurementDate": "ตัวอย่างวันที่วัด",
                "route": "ตัวอย่างข้อมูลเส้นทางการเดินรถ",
                "province": "ตัวอย่างจังหวัด",
                "district": "ตัวอย่างอำเภอ",
                "complianceStandard": "ตัวอย่างมาตรฐานสิ่งแวดล้อมที่อ้างอิง",
                "remark": "ตัวอย่างหมายเหตุ"
            }
        ]
        """
        created_environments = []

        try:
            for environment_data in request.data:
                from mas.models import MasProvince, MasDistrict
                from service_fares.models import Route as Route

                # Handle route lookup
                route_obj = None
                if environment_data.get('route'):
                    route_value = environment_data.get('route')
                    try:
                        if isinstance(route_value, int) or (isinstance(route_value, str) and route_value.isdigit()):
                            route_obj = Route.objects.get(id=int(route_value))
                        else:
                            route_obj = Route.objects.filter(name__icontains=route_value).first()
                    except:
                        route_obj = None

                # Handle province lookup
                province_obj = None
                if environment_data.get('province'):
                    province_value = environment_data.get('province')
                    try:
                        if isinstance(province_value, int) or (isinstance(province_value, str) and province_value.isdigit()):
                            province_obj = MasProvince.objects.get(id=int(province_value))
                        else:
                            province_obj = MasProvince.objects.filter(name__icontains=province_value).first()
                    except:
                        province_obj = None

                # Handle district lookup
                district_obj = None
                if environment_data.get('district'):
                    district_value = environment_data.get('district')
                    try:
                        if isinstance(district_value, int) or (isinstance(district_value, str) and district_value.isdigit()):
                            district_obj = MasDistrict.objects.get(id=int(district_value))
                        else:
                            district_obj = MasDistrict.objects.filter(name__icontains=district_value).first()
                    except:
                        district_obj = None

                # Handle value conversion
                value = 0
                try:
                    value_data = environment_data.get('value', 0)
                    if isinstance(value_data, (int, float)):
                        value = float(value_data)
                    elif isinstance(value_data, str):
                        # Try to convert string to float, default to 0 if fails
                        try:
                            value = float(value_data)
                        except:
                            value = 0
                except:
                    value = 0

                # Handle measurementDate conversion
                from datetime import datetime
                measurement_date = None
                try:
                    date_data = environment_data.get('measurementDate')
                    if date_data:
                        if isinstance(date_data, str):
                            # Try different date formats
                            date_formats = [
                                '%Y-%m-%d',
                                '%Y-%m-%dT%H:%M:%S',
                                '%Y-%m-%d %H:%M:%S',
                                '%d/%m/%Y',
                                '%d-%m-%Y'
                            ]
                            for date_format in date_formats:
                                try:
                                    measurement_date = datetime.strptime(date_data, date_format)
                                    break
                                except:
                                    continue
                            # If no format worked, use current datetime
                            if measurement_date is None:
                                measurement_date = datetime.now()
                        else:
                            measurement_date = date_data
                    else:
                        measurement_date = datetime.now()
                except:
                    measurement_date = datetime.now()

                # Create environment record directly without validation
                environment = Environment.objects.create(
                    parameter=environment_data.get('parameter', ''),
                    value=value,
                    unit=environment_data.get('unit', ''),
                    measurementDate=measurement_date,
                    route=route_obj,
                    masProvince=province_obj,
                    masDistrict=district_obj,
                    complianceStandard=environment_data.get('complianceStandard', ''),
                    remark=environment_data.get('remark', '')
                )

                created_environments.append({
                    "id": environment.id,
                    "parameter": environment.parameter,
                    "value": environment.value,
                    "unit": environment.unit,
                    "measurementDate": environment.measurementDate.isoformat() if environment.measurementDate else None,
                    "complianceStandard": environment.complianceStandard,
                    "remark": environment.remark
                })

            return Response(
                {
                    "message": f"Import completed. Created {len(created_environments)} environment records.",
                    "created_environments": created_environments,
                    "created_count": len(created_environments),
                    "total_count": len(request.data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "message": str(e),
                    "created_count": len(created_environments)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
