from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.decorators import action
from utils.pagination import CustomPagination
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import Django<PERSON>ilterBackend
from rest_framework import filters
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>arser, JSONParser
from rest_framework.response import Response
from django.db.models import Q
from django.db import transaction

from .models import ManpowerQualifications
from .serializers import ManpowerQualificationsSerializer


@extend_schema(
    tags=["Manpower"]
)
class ManpowerQualificationsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ManpowerQualifications model providing CRUD operations.
    """
    queryset = ManpowerQualifications.objects.all()
    serializer_class = ManpowerQualificationsSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ars<PERSON>)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['code','name','organization','qualification','fieldOfStudy','experienceYears','skill','isCertification','status']
    search_fields = ['code','name','organization','qualification','fieldOfStudy','experienceYears','skill','isCertification','status']
    
    def list(self, request, *args, **kwargs):
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        organization = request.query_params.get('organization')
        qualification = request.query_params.get('qualification')
        fieldOfStudy = request.query_params.get('fieldOfStudy')
        experienceYears = request.query_params.get('experienceYears')
        skill = request.query_params.get('skill')
        isCertification = request.query_params.get('isCertification')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        queryset = self.get_queryset()
        if code:
            queryset = queryset.filter(code__icontains=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if organization:
            queryset = queryset.filter(organization__icontains=organization)
        if qualification:
            queryset = queryset.filter(qualification=qualification)
        if fieldOfStudy:
            queryset = queryset.filter(fieldOfStudy__icontains=fieldOfStudy)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if skill:
            queryset = queryset.filter(skill__icontains=skill)
        if isCertification:
            queryset = queryset.filter(isCertification=isCertification)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            queryset = queryset.order_by(ordering)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_manpower_qualifications(self, request, *args, **kwargs):
        """
        Import ManpowerQualifications from list data
        Request Body:
        [
            {
                "code": "ตัวอย่างรหัสบุคลากร",
                "organization": "ตัวสังกัดหน่วยงาน",
                "name": "ตัวอย่างชื่อ-นามสกุล",
                "qualification": "ตัวอย่างวุฒิการศึกษา",
                "fieldOfStudy": "ตัวอย่างสาขาวิชา",
                "experienceYears": "ตัวอย่างปีประสบการณ์",
                "skill": "ตัวอย่างทักษะเฉพาะด้าน",
                "isCertification": "ตัวอย่างใบรับรองวิชาชีพ"
            }
        ]
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of manpower qualification objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_records = []
        errors = []

        # Qualification mapping based on data dictionary
        qualification_mapping = {
            'ปริญญาตรี': 'B',
            'ปริญญาโท': 'M',
            'ปริญญาเอก': 'D',
            'ปวช.': 'V',
            'ปวส.': 'H',
            'B': 'B',
            'M': 'M',
            'D': 'D',
            'V': 'V',
            'H': 'H'
        }

        # Certification mapping
        certification_mapping = {
            'มี': True,
            'ไม่มี': False,
            '1': True,
            '0': False,
            True: True,
            False: False
        }

        with transaction.atomic():
            for index, item in enumerate(request.data):
                try:
                    # Validate required fields
                    required_fields = ['code', 'name', 'organization', 'qualification', 'fieldOfStudy', 'experienceYears', 'skill', 'isCertification']
                    missing_fields = [field for field in required_fields if not item.get(field)]

                    if missing_fields:
                        errors.append({
                            'index': index,
                            'error': f'Missing required fields: {", ".join(missing_fields)}',
                            'data': item
                        })
                        continue

                    # Map qualification
                    qualification_input = str(item.get('qualification', '')).strip()
                    qualification = qualification_mapping.get(qualification_input)
                    if not qualification:
                        qualification = 'B'

                    # Map certification
                    certification_input = item.get('isCertification')
                    if isinstance(certification_input, str):
                        certification_input = certification_input.strip()

                    is_certification = certification_mapping.get(certification_input)
                    if is_certification is None:
                        is_certification = 0

                    # Validate experience years
                    try:
                        experience_years = int(item.get('experienceYears', 0))
                        if experience_years < 0:
                            raise ValueError("Experience years cannot be negative")
                    except (ValueError, TypeError):
                        experience_years = 0

                    # Check if code already exists
                    if ManpowerQualifications.objects.filter(code=item.get('code')).exists():
                        errors.append({
                            'index': index,
                            'error': f'Code "{item.get("code")}" already exists',
                            'data': item
                        })
                        continue

                    # Create the record
                    manpower_qualification = ManpowerQualifications.objects.create(
                        code=item.get('code').strip(),
                        name=item.get('name').strip(),
                        organization=item.get('organization').strip(),
                        qualification=qualification,
                        fieldOfStudy=item.get('fieldOfStudy').strip(),
                        experienceYears=experience_years,
                        skill=item.get('skill').strip(),
                        isCertification=is_certification,
                        status=True  # Default to active
                    )

                    created_records.append(manpower_qualification)

                except Exception as e:
                    errors.append({
                        'index': index,
                        'error': f'Unexpected error: {str(e)}',
                        'data': item
                    })

        # Prepare response
        response_data = {
            "message": f"Successfully imported {len(created_records)} manpower qualification records",
            "created_count": len(created_records),
            "error_count": len(errors)
        }

        if created_records:
            serializer = self.get_serializer(created_records, many=True)
            response_data["created_records"] = serializer.data

        if errors:
            response_data["errors"] = errors

        # Return appropriate status code
        if errors and not created_records:
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        elif errors and created_records:
            return Response(response_data, status=status.HTTP_207_MULTI_STATUS)
        else:
            return Response(response_data, status=status.HTTP_201_CREATED)
