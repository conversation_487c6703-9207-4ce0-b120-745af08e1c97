from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOr<PERSON>eadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ars<PERSON>
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from django.db.models import Q
from django.db import transaction
from datetime import datetime
from testings.models import Runningnumber
from .models import Expert
from .serializers import ExpertSerializer


@extend_schema(
    tags=["Expert"]
)
class ExpertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Expert model providing CRUD operations.
    Supports file uploads for certifications field.
    """
    queryset = Expert.objects.all()
    serializer_class = ExpertSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSO<PERSON>arser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['code','name','affiliation','specialization','experienceYears','status']
    search_fields = ['code','name','affiliation','specialization','experienceYears','status']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        affiliation = request.query_params.get('affiliation')
        specialization = request.query_params.get('specialization')
        experienceYears = request.query_params.get('experienceYears')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        if code:
            queryset = queryset.filter(code=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if affiliation:
            queryset = queryset.filter(affiliation__icontains=affiliation)
        if specialization:
            queryset = queryset.filter(specialization=specialization)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new Expert with auto-generated code
        """
        try:
            with transaction.atomic():
                # Get the last running number for Expert (type 'E')
                lastRunningNumber = Runningnumber.objects.select_for_update().filter(type='E').order_by('-number').first()
                if lastRunningNumber:
                    nextNumber = lastRunningNumber.number + 1
                else:
                    nextNumber = 1

                # Create a mutable copy of request data and add code
                data = request.data.copy()
                data['code'] = f"E{nextNumber:04d}"

                # Create the expert
                serializer = self.get_serializer(data=data)
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)

                # If we got here, creation was successful - update running number
                if lastRunningNumber:
                    lastRunningNumber.number = nextNumber
                    lastRunningNumber.save()
                else:
                    # Create new running number if it doesn't exist
                    current_year = datetime.now().year
                    Runningnumber.objects.create(
                        type='E',
                        year=current_year,
                        number=nextNumber
                    )

                headers = self.get_success_headers(serializer.data)
                return Response(serializer.data, status=201, headers=headers)
        except Exception as e:
            # Log the error and re-raise it
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating Expert: {str(e)}")
            raise

    @action(detail=False, methods=['post'], url_path='import')
    def import_experts(self, request):
        """
        Import multiple experts from a list with auto-generated codes
        Expected data format:
        [
            {
                "name": "ตัวชื่อ - นามสกุล",
                "affiliation": "ตัวอย่างหน่วยงาน/บริษัท",
                "specialization": "ตัวอย่างความเชี่ยวชาญ",
                "experienceYears": "ตัวอย่างจำนวนปีประสบการณ์",
                "phone": "ตัวอย่างเบอร์โทรติดต่อ",
                "email": "ตัวอีเมลติดต่อ",
                "remark": "ตัวอย่างหมายเหตุ"
            }
        ]
        """
        try:
            experts_data = request.data

            # Validate that the data is a list
            if not isinstance(experts_data, list):
                return Response(
                    {"error": "Data must be a list of expert objects"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not experts_data:
                return Response(
                    {"error": "List cannot be empty"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            created_experts = []
            errors = []

            with transaction.atomic():
                # Get the last running number for Expert (type 'E')
                lastRunningNumber = Runningnumber.objects.select_for_update().filter(type='E').order_by('-number').first()
                if lastRunningNumber:
                    nextNumber = lastRunningNumber.number + 1
                else:
                    nextNumber = 1

                for index, expert_data in enumerate(experts_data):
                    try:
                        # Create a mutable copy and add auto-generated code
                        data = expert_data.copy()
                        data['code'] = f"E{nextNumber:04d}"

                        # Check and validate experienceYears field
                        if 'experienceYears' in data:
                            experience_years = data['experienceYears']
                            # Check if it's a valid integer
                            if isinstance(experience_years, str):
                                # Try to convert string to integer
                                try:
                                    data['experienceYears'] = int(experience_years)
                                except (ValueError, TypeError):
                                    # If conversion fails, set to default 0
                                    data['experienceYears'] = 0
                            elif not isinstance(experience_years, int):
                                # If it's not string or int, set to default 0
                                data['experienceYears'] = 0
                        else:
                            # If experienceYears field is missing, set to default 0
                            data['experienceYears'] = 0

                        # Create the expert
                        serializer = self.get_serializer(data=data)
                        if serializer.is_valid():
                            serializer.save()
                            created_experts.append(serializer.data)
                            nextNumber += 1
                        else:
                            errors.append({
                                "index": index,
                                "data": expert_data,
                                "errors": serializer.errors
                            })
                    except Exception as e:
                        errors.append({
                            "index": index,
                            "data": expert_data,
                            "error": str(e)
                        })

                # If there are any errors, rollback the transaction
                if errors:
                    transaction.set_rollback(True)
                    return Response(
                        {
                            "error": "Import failed due to validation errors",
                            "errors": errors,
                            "created_count": 0,
                            "total_count": len(experts_data)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Update the running number
                if lastRunningNumber:
                    lastRunningNumber.number = nextNumber - 1
                    lastRunningNumber.save()
                else:
                    # Create new running number if it doesn't exist
                    current_year = datetime.now().year
                    Runningnumber.objects.create(
                        type='E',
                        year=current_year,
                        number=nextNumber - 1
                    )

            return Response(
                {
                    "message": "Experts imported successfully",
                    "created_experts": created_experts,
                    "created_count": len(created_experts),
                    "total_count": len(experts_data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error importing experts: {str(e)}")
            return Response(
                {"error": f"Import failed: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

