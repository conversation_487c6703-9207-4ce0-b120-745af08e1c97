from django.db import models
from RTRDA.model import BaseModel


class MaintenanceWork(BaseModel):
    code = models.CharField(db_column='Code', max_length=200, db_collation='Thai_CI_AI')
    systemComponent = models.CharField(db_column='SystemComponent', max_length=500, db_collation='Thai_CI_AI')
    maintenanceType = models.CharField(db_column='MaintenanceType', max_length=1, db_collation='Thai_CI_AI')
    scheduleDate = models.DateTimeField(db_column='ScheduleDate')
    actualDate = models.DateTimeField(db_column='ActualDate', blank=True, null=True)
    technician = models.CharField(db_column='Technician', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    status = models.BooleanField(db_column='Status', default=False)
    costEstimate = models.FloatField(db_column='CostEstimate')
    remark = models.TextField(db_column='Remark', db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'MaintenanceWork'

