#!/usr/bin/env python3
"""
Example script demonstrating how to use the ServiceFares import API endpoint.

This script shows how to import ServiceFares data using the new import method.
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"  # Adjust this to your server URL
API_ENDPOINT = f"{BASE_URL}/service-fares/import/"

# Example data to import
# Note: You need to specify routeId as a query parameter
ROUTE_ID = 1  # Replace with an actual route ID from your database

sample_data = [
    {
        "originStation": "Bangkok Central Station",
        "destinationStation": "Chiang Mai Station",
        "fareType": "N",  # N=ปกติ (Normal)
        "fareAmount": 150.0,
        "serviceType": "N",  # N=รถปกติ (Regular train)
        "frequency": "Daily service",
        "operationStart": "06:00",
        "operationEnd": "22:00"
    },
    {
        "originStation": "Bangkok Central Station", 
        "destinationStation": "Chiang Mai Station",
        "fareType": "C",  # C=เด็ก (Child)
        "fareAmount": 75.0,
        "serviceType": "E",  # E=รถด่วน (Express train)
        "frequency": "Daily service",
        "operationStart": "08:00",
        "operationEnd": "20:00"
    },
    {
        "originStation": "Bangkok Central Station",
        "destinationStation": "Chiang Mai Station", 
        "fareType": "O",  # O=ผู้สูงอายุ (Senior)
        "fareAmount": 100.0,
        "serviceType": "N",  # N=รถปกติ (Regular train)
        "frequency": "Daily service",
        "operationStart": "06:00",
        "operationEnd": "22:00"
    }
]

def import_servicefares(data, route_id, auth_token=None):
    """
    Import ServiceFares data using the API endpoint.
    
    Args:
        data (list): List of ServiceFares data to import
        route_id (int): ID of the route to associate with the fares
        auth_token (str, optional): Authentication token if required
    
    Returns:
        dict: API response
    """
    headers = {
        'Content-Type': 'application/json',
    }
    
    # Add authentication header if token is provided
    if auth_token:
        headers['Authorization'] = f'Bearer {auth_token}'
    
    # Make the API request
    url = f"{API_ENDPOINT}?routeId={route_id}"
    
    try:
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Failed to decode JSON response: {e}")
        print(f"Raw response: {response.text}")
        return None

def main():
    """Main function to demonstrate the import functionality."""
    print("ServiceFares Import Example")
    print("=" * 50)
    
    print(f"Importing {len(sample_data)} ServiceFares records...")
    print(f"Route ID: {ROUTE_ID}")
    print(f"API Endpoint: {API_ENDPOINT}")
    print()
    
    # Show the data being imported
    print("Data to import:")
    print(json.dumps(sample_data, indent=2, ensure_ascii=False))
    print()
    
    # Import the data
    result = import_servicefares(sample_data, ROUTE_ID)
    
    if result:
        if 'error' in result:
            print("❌ Import failed!")
            print(f"Error: {result['error']}")
            if 'errors' in result:
                print("Detailed errors:")
                for error in result['errors']:
                    print(f"  - Index {error['index']}: {error['error']}")
        else:
            print("✅ Import successful!")
            print(f"Created {result['created_count']} out of {result['total_count']} records")
    else:
        print("❌ Failed to get response from API")

if __name__ == "__main__":
    main()
