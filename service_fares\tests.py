from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Servicefares, Route
from mas.models import MasTrainType, MasRailwayLine

User = get_user_model()


class ServicefaresImportTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test MasTrainType and MasRailwayLine
        self.mas_train_type = MasTrainType.objects.create(name='Test Train Type')
        self.mas_railway_line = MasRailwayLine.objects.create(name='Test Railway Line')

        # Create a test Route
        self.route = Route.objects.create(
            masTrainType=self.mas_train_type,
            masRailwayLine=self.mas_railway_line,
            name='Test Route',
            origin='Bangkok',
            destination='Chiang Mai',
            status=True
        )

        # Authenticate the user
        self.client.force_authenticate(user=self.user)

    def test_import_servicefares_success(self):
        """Test successful import of servicefares"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "N",
                "fareAmount": 150.0,
                "serviceType": "N",
                "frequency": "Daily",
                "operationStart": "06:00",
                "operationEnd": "22:00"
            },
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "C",
                "fareAmount": 75.0,
                "serviceType": "E",
                "frequency": "Daily",
                "operationStart": "08:00",
                "operationEnd": "20:00"
            }
        ]

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['created_count'], 2)
        self.assertEqual(response.data['total_count'], 2)
        self.assertEqual(len(response.data['created_servicefares']), 2)

        # Verify records were created in database
        self.assertEqual(Servicefares.objects.count(), 2)

        # Verify first record
        first_record = Servicefares.objects.filter(fareType='N').first()
        self.assertEqual(first_record.originStation, "Bangkok Central")
        self.assertEqual(first_record.destinationStation, "Chiang Mai Central")
        self.assertEqual(first_record.fareAmount, 150.0)
        self.assertEqual(first_record.serviceType, "N")

    def test_import_servicefares_missing_route_id(self):
        """Test import without routeId parameter"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "N",
                "fareAmount": 150.0,
                "serviceType": "N"
            }
        ]

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("routeId query parameter is required", response.data['error'])

    def test_import_servicefares_invalid_route_id(self):
        """Test import with non-existent routeId"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "N",
                "fareAmount": 150.0,
                "serviceType": "N"
            }
        ]

        response = self.client.post(f'{url}?routeId=99999', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Route with id 99999 does not exist", response.data['error'])

    def test_import_servicefares_missing_required_fields(self):
        """Test import with missing required fields"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                # Missing destinationStation, fareType, fareAmount, serviceType
            }
        ]

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['created_count'], 0)
        self.assertEqual(len(response.data['errors']), 1)
        self.assertIn("Missing required fields", response.data['errors'][0]['error'])

    def test_import_servicefares_invalid_fare_type(self):
        """Test import with invalid fareType"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "X",  # Invalid fare type
                "fareAmount": 150.0,
                "serviceType": "N"
            }
        ]

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['created_count'], 0)
        self.assertIn("Invalid fareType", response.data['errors'][0]['error'])

    def test_import_servicefares_invalid_service_type(self):
        """Test import with invalid serviceType"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "N",
                "fareAmount": 150.0,
                "serviceType": "X"  # Invalid service type
            }
        ]

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['created_count'], 0)
        self.assertIn("Invalid serviceType", response.data['errors'][0]['error'])

    def test_import_servicefares_invalid_fare_amount(self):
        """Test import with invalid fareAmount"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "N",
                "fareAmount": "invalid",  # Invalid fare amount
                "serviceType": "N"
            }
        ]

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['created_count'], 0)
        self.assertIn("fareAmount must be a valid number", response.data['errors'][0]['error'])

    def test_import_servicefares_negative_fare_amount(self):
        """Test import with negative fareAmount"""
        url = reverse('servicefares-import')
        data = [
            {
                "originStation": "Bangkok Central",
                "destinationStation": "Chiang Mai Central",
                "fareType": "N",
                "fareAmount": -50.0,  # Negative fare amount
                "serviceType": "N"
            }
        ]

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['created_count'], 0)
        self.assertIn("fareAmount must be a positive number", response.data['errors'][0]['error'])

    def test_import_servicefares_not_list(self):
        """Test import with non-list data"""
        url = reverse('servicefares-import')
        data = {
            "originStation": "Bangkok Central",
            "destinationStation": "Chiang Mai Central",
            "fareType": "N",
            "fareAmount": 150.0,
            "serviceType": "N"
        }

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Request data must be a list", response.data['error'])

    def test_import_servicefares_empty_list(self):
        """Test import with empty list"""
        url = reverse('servicefares-import')
        data = []

        response = self.client.post(f'{url}?routeId={self.route.id}', data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("List cannot be empty", response.data['error'])
