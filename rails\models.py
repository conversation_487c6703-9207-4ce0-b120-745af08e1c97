from django.db import models
from RTRDA.model import BaseModel
from mas.models import MasTrainType, MasRailwayLine

class RailServiceProvider(BaseModel):
    id = models.AutoField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    masTrainType = models.ForeignKey(MasTrainType, on_delete=models.CASCADE, db_column='MasTrainTypeId')
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI')
    logo = models.ImageField(db_column='Logo', upload_to='rails/logo/', max_length=100, blank=True, null=True)
    status = models.BooleanField(db_column='Status')
    masRailwayLine = []

    class Meta:
        managed = False
        db_table = 'RailServiceProvider'


class RailServiceProviderMasRailwayLine(models.Model):
    railServiceProvider = models.ForeignKey(RailServiceProvider, on_delete=models.CASCADE, db_column='RailServiceProviderId')
    masRailwayLine = models.ForeignKey(MasRailwayLine, on_delete=models.CASCADE, db_column='MasRailwayLineId')

    class Meta:
        managed = False
        db_table = 'RailServiceProviderMasRailwayLine'
        unique_together = (('railServiceProvider', 'masRailwayLine'),)


class RailwayEngineeringStandard(BaseModel):
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    file = models.FileField(db_column='File', upload_to='rails/file/', max_length=100)
    cover = models.ImageField(db_column='Cover', upload_to='rails/cover/', max_length=100)
    status = models.BooleanField(db_column='Status')
    views = models.IntegerField(db_column='Views', default=0)

    class Meta:
        managed = False
        db_table = 'RailwayEngineeringStandard'


class AgencySupportRailwayResearch(BaseModel):
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    link = models.CharField(db_column='Link', max_length=500, db_collation='Thai_CI_AI')
    logo = models.ImageField(db_column='Logo', upload_to='agency-support/logo/', max_length=100)
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'AgencySupportRailwayResearch'


class UnderConstruction(BaseModel):
    code = models.CharField(db_column='Code', max_length=20, db_collation='Thai_CI_AI')
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    detail = models.CharField(db_column='Detail', max_length=1000, db_collation='Thai_CI_AI')
    masRailwayLine = models.ForeignKey(MasRailwayLine, on_delete=models.PROTECT, db_column='MasRailwayLineId')
    latitude = models.FloatField(db_column='Latitude')
    longitude = models.FloatField(db_column='Longitude')
    status = models.BooleanField(db_column='Status', default=True)

    class Meta:
        managed = False
        db_table = 'UnderConstruction'
